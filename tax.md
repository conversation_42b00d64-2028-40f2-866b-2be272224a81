# China Tax Calculator

一个用于计算中国个人所得税的 TypeScript 库，基于累计预扣法进行计算。

## 特性

- 🧮 **准确计算**：基于中国个人所得税法的累计预扣法
- 📊 **多种计算模式**：支持单月、多月、项目分组等多种计算方式
- 🔧 **TypeScript 支持**：完整的类型定义，提供良好的开发体验
- 📦 **轻量级**：无外部依赖，体积小巧
- ✅ **测试覆盖**：完整的单元测试覆盖

## 安装

```bash
npm install china-tax-calculator
```

或使用 yarn：

```bash
yarn add china-tax-calculator
```

## 使用方法

### 基础用法

```typescript
import { calculateTaxByMonths } from 'china-tax-calculator';

// 计算多月收入的个人所得税
const monthIncomes = [10000, 15000, 20000];
const monthNumbers = ['第1月', '第2月', '第3月'];

const results = calculateTaxByMonths(monthIncomes, monthNumbers);
console.log(results);
```

### 分组计算

```typescript
import { calculateTaxByMonthsGrouped } from 'china-tax-calculator';

// 获取分组的计算结果（处理断档月份）
const monthIncomes = [10000, 15000, 20000];
const monthNumbers = ['第1月', '第2月', '第5月']; // 第3、4月断档

const groups = calculateTaxByMonthsGrouped(monthIncomes, monthNumbers);
console.log(groups);
// 输出：
// [
//   {
//     groupIndex: 0,
//     title: '计算组 1：第1月 至 第2月',
//     data: [...]
//   },
//   {
//     groupIndex: 1,
//     title: '计算组 2：第5月',
//     data: [...]
//   }
// ]
```

### 项目分组计算

```typescript
import { calculateTaxByProjects, ProjectIncomeData } from 'china-tax-calculator';

const projectsData: ProjectIncomeData[] = [
  {
    projectId: '1',
    projectName: '项目A',
    monthIncomes: [10000, 15000],
    monthNumbers: ['第1月', '第2月'],
  },
  {
    projectId: '2',
    projectName: '项目B',
    monthIncomes: [20000],
    monthNumbers: ['第1月'],
  },
];

const results = calculateTaxByProjects(projectsData);
console.log(results);
```

### 合并项目计算

```typescript
import { calculateTaxByProjectsMerged } from 'china-tax-calculator';

// 合并所有项目的同月份收入进行计算
const mergedResults = calculateTaxByProjectsMerged(projectsData);
console.log(mergedResults);
```

## API 文档

### 主要函数

#### `calculateTaxByMonths(monthIncomes, monthNumbers)`

计算多月收入的个人所得税。

**参数：**
- `monthIncomes: number[]` - 每月收入数组
- `monthNumbers: string[]` - 月份标识数组

**返回：** `TaxCalculationResult[]`

#### `calculateTaxByMonthsGrouped(monthIncomes, monthNumbers)`

分组计算多月收入的个人所得税，自动处理断档月份。

**参数：**
- `monthIncomes: number[]` - 每月收入数组
- `monthNumbers: string[]` - 月份标识数组

**返回：** `TaxCalculationGroup[]`

#### `calculateTaxByProjects(projectsData)`

按项目分组计算税收。

**参数：**
- `projectsData: ProjectIncomeData[]` - 项目收入数据数组

**返回：** `ProjectTaxCalculationGroup[]`

#### `calculateTaxByProjectsMerged(projectsData)`

合并所有项目的同月份收入并计算税收。

**参数：**
- `projectsData: ProjectIncomeData[]` - 项目收入数据数组

**返回：** `TaxCalculationGroup[]`

### 类型定义

```typescript
interface TaxCalculationResult {
  month: string;
  income: number;
  cumulativeIncome: number;
  cumulativeCost: number;
  cumulativeFixedDeduct: number;
  cumulativeTaxableIncome: number;
  rate: number;
  quickDeduction: number;
  cumulativeTax: number;
  lastCumulativeTax: number;
  currentTax: number;
}

interface TaxCalculationGroup {
  groupIndex: number;
  title: string;
  data: TaxCalculationResult[];
}

interface ProjectIncomeData {
  projectId: string;
  projectName: string;
  monthIncomes: number[];
  monthNumbers: string[];
}
```

## 计算规则

本库基于中国个人所得税法的累计预扣法进行计算：

- **成本扣除率**：20%
- **每月固定扣除**：5000元
- **税率级次**：采用7级超额累进税率（3% - 45%）

### 税率表

| 级数 | 累计预扣预缴应纳税所得额 | 预扣率(%) | 速算扣除数 |
|------|------------------------|-----------|------------|
| 1    | 不超过36,000元的部分    | 3         | 0          |
| 2    | 超过36,000元至144,000元 | 10        | 2,520      |
| 3    | 超过144,000元至300,000元| 20        | 16,920     |
| 4    | 超过300,000元至420,000元| 25        | 31,920     |
| 5    | 超过420,000元至660,000元| 30        | 52,920     |
| 6    | 超过660,000元至960,000元| 35        | 85,920     |
| 7    | 超过960,000元的部分     | 45        | 181,920    |

## 开发

```bash
# 安装依赖
npm install

# 构建
npm run build

# 运行测试
npm test

# 开发模式（监听文件变化）
npm run dev
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### 1.0.0

- 初始版本发布
- 支持累计预扣法计算
- 支持项目分组和合并计算
- 完整的 TypeScript 类型支持
