<template>
  <div class="signature-container">
    <div class="signature-header">
      <h2>手写签名</h2>
      <p class="tip">请在下方横屏区域内进行签名</p>
      <p class="sub-tip">建议将设备横向放置以获得更好的签名体验</p>
    </div>

    <div class="signature-canvas-wrapper">
      <canvas
        ref="signatureCanvas"
        class="signature-canvas"
        @touchstart="startDrawing"
        @touchmove="draw"
        @touchend="stopDrawing"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        @mouseleave="stopDrawing"
      ></canvas>
    </div>

    <div class="signature-actions">
      <van-button
        class="action-btn clear-btn"
        @click="clearSignature"
        type="default"
        size="large"
      >
        清除
      </van-button>

      <van-button
        class="action-btn save-btn"
        @click="saveSignature"
        type="primary"
        size="large"
        :disabled="!hasSignature"
      >
        保存签名
      </van-button>
    </div>

    <!-- 预览签名 -->
    <div v-if="signatureImage" class="signature-preview">
      <h3>签名预览</h3>
      <img :src="signatureImage" alt="签名预览" class="preview-image" />
      <div class="preview-actions">
        <van-button
          @click="downloadSignature"
          type="info"
          size="small"
        >
          下载图片
        </van-button>
        <van-button
          @click="copyBase64"
          type="warning"
          size="small"
        >
          复制Base64
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Signature',
  data () {
    return {
      canvas: null,
      ctx: null,
      isDrawing: false,
      hasSignature: false,
      signatureImage: null,
      lastX: 0,
      lastY: 0
    }
  },
  mounted () {
    this.initCanvas()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    window.addEventListener('orientationchange', this.handleResize)
  },

  beforeDestroy () {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize)
    window.removeEventListener('orientationchange', this.handleResize)
  },
  methods: {
    initCanvas () {
      this.canvas = this.$refs.signatureCanvas
      this.ctx = this.canvas.getContext('2d')

      // 设置画布尺寸 - 占满全屏
      const container = this.canvas.parentElement
      const containerWidth = container.clientWidth

      // 计算可用高度：视窗高度 - 头部 - 按钮区域 - 边距 - 预览区域
      const viewportHeight = window.innerHeight
      const headerHeight = 80 // 头部区域高度
      const buttonHeight = 60 // 按钮区域高度
      const previewHeight = this.signatureImage ? 150 : 0 // 预览区域高度
      const padding = 40 // 总边距
      const availableHeight = viewportHeight - headerHeight - buttonHeight - previewHeight - padding

      this.canvas.width = containerWidth
      this.canvas.height = Math.max(200, availableHeight)

      // 设置画布的CSS尺寸与实际尺寸一致
      this.canvas.style.width = this.canvas.width + 'px'
      this.canvas.style.height = this.canvas.height + 'px'

      // 设置画笔样式
      this.ctx.strokeStyle = '#000000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
      this.ctx.lineJoin = 'round'

      // 设置透明背景
      this.ctx.globalCompositeOperation = 'source-over'
    },

    getEventPos (e) {
      const rect = this.canvas.getBoundingClientRect()
      const clientX = e.clientX || (e.touches && e.touches[0].clientX)
      const clientY = e.clientY || (e.touches && e.touches[0].clientY)

      // 计算缩放比例
      const scaleX = this.canvas.width / rect.width
      const scaleY = this.canvas.height / rect.height

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY
      }
    },

    startDrawing (e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = true
      const pos = this.getEventPos(e)
      this.lastX = pos.x
      this.lastY = pos.y

      // 开始新路径
      this.ctx.beginPath()
      this.ctx.moveTo(this.lastX, this.lastY)

      // 绘制起始点
      this.ctx.lineTo(pos.x, pos.y)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo(pos.x, pos.y)
    },

    draw (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()

      const pos = this.getEventPos(e)

      // 使用二次贝塞尔曲线使线条更平滑
      this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)

      this.lastX = pos.x
      this.lastY = pos.y
      this.hasSignature = true
    },

    stopDrawing (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = false
      this.ctx.closePath()
    },

    clearSignature () {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      this.hasSignature = false
      this.signatureImage = null

      // 重新计算画布大小
      this.updateCanvasSize()
    },

    saveSignature () {
      if (!this.hasSignature) {
        this.$toast('请先进行签名')
        return
      }

      // 生成透明背景的PNG图片
      this.signatureImage = this.canvas.toDataURL('image/png')

      this.$toast.success('签名保存成功')

      // 重新计算画布大小以适应预览区域
      this.updateCanvasSize()

      // 可以在这里添加保存到服务器的逻辑
      // 注意：不调用 onSignatureSaved 以避免自动跳转页面
    },

    downloadSignature () {
      if (!this.signatureImage) return

      const link = document.createElement('a')
      link.download = `signature_${Date.now()}.png`
      link.href = this.signatureImage
      link.click()
    },

    copyBase64 () {
      if (!this.signatureImage) return

      // 复制base64到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.signatureImage).then(() => {
          this.$toast.success('Base64已复制到剪贴板')
        }).catch(() => {
          this.$toast.fail('复制失败')
        })
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea')
        textArea.value = this.signatureImage
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$toast.success('Base64已复制到剪贴板')
      }
    },

    handleResize () {
      // 延迟执行以确保DOM更新完成
      setTimeout(() => {
        if (this.canvas && this.ctx) {
          // 保存当前绘制内容
          const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)

          // 重新初始化画布
          this.initCanvas()

          // 如果有签名内容，尝试恢复
          if (this.hasSignature) {
            this.ctx.putImageData(imageData, 0, 0)
          }
        }
      }, 100)
    },

    // 当签名图片状态改变时重新计算画布大小
    updateCanvasSize () {
      this.$nextTick(() => {
        this.handleResize()
      })
    },

    onSignatureSaved (base64Image) {
      // 这里可以添加保存签名的业务逻辑
      // 比如上传到服务器、存储到本地等
      console.log('签名已保存:', base64Image)

      // 暂时禁用所有自动跳转逻辑，让用户停留在当前页面
      // 用户可以继续签名或手动返回
      
      // 如果需要返回功能，可以取消下面的注释
      // const returnUrl = this.$route.query.returnUrl
      // if (returnUrl) {
      //   this.$router.push({
      //     path: returnUrl,
      //     query: {
      //       signatureData: base64Image
      //     }
      //   })
      // }
    }
  }
}
</script>

<style scoped lang="less">
.signature-container {
  padding: 10px;
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .signature-header {
    text-align: center;
    margin-bottom: 10px;
    flex-shrink: 0;

    h2 {
      color: #333;
      font-size: 20px;
      margin-bottom: 5px;
    }

    .tip {
      color: #666;
      font-size: 13px;
      margin: 0 0 3px 0;
    }

    .sub-tip {
      color: #999;
      font-size: 11px;
      margin: 0;
      font-style: italic;
    }
  }

  .signature-canvas-wrapper {
    background: white;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;

    &::before {
      content: '签名区域';
      position: absolute;
      top: 15px;
      left: 15px;
      color: #ccc;
      font-size: 12px;
      pointer-events: none;
      z-index: 1;
    }

    .signature-canvas {
      width: 100%;
      max-width: 100%;
      height: 100%;
      min-height: 200px;
      border: 1px solid #eee;
      border-radius: 4px;
      cursor: crosshair;
      touch-action: none;
      display: block;
      flex: 1;
      background: linear-gradient(90deg, transparent 24px, #f0f0f0 25px, #f0f0f0 26px, transparent 27px),
                  linear-gradient(#f9f9f9 24px, transparent 25px, transparent 26px, #f9f9f9 27px);
      background-size: 25px 25px;

      &:hover {
        border-color: #1989fa;
      }

      &:focus {
        outline: none;
        border-color: #1989fa;
        box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.2);
      }
    }
  }

  .signature-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    flex-shrink: 0;

    .action-btn {
      flex: 1;
      height: 45px;
      font-size: 15px;
      border-radius: 8px;
    }

    .clear-btn {
      background: #f5f5f5;
      border-color: #d9d9d9;
      color: #666;

      &:hover {
        background: #e6e6e6;
        border-color: #bfbfbf;
      }
    }

    .save-btn {
      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
      }
    }
  }

  .signature-preview {
    background: white;
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    flex-shrink: 0;

    h3 {
      color: #333;
      font-size: 14px;
      margin-bottom: 8px;
      text-align: center;
    }

    .preview-image {
      width: 100%;
      max-width: 200px;
      max-height: 60px;
      object-fit: contain;
      border: 1px solid #eee;
      border-radius: 3px;
      display: block;
      margin: 0 auto 8px;
      background:
        repeating-conic-gradient(#f0f0f0 0% 25%, transparent 0% 50%)
        50% / 10px 10px;
    }

    .preview-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .van-button {
        min-width: 80px;
        height: 32px;
        font-size: 12px;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .signature-container {
    padding: 8px;

    .signature-header {
      margin-bottom: 8px;

      h2 {
        font-size: 18px;
        margin-bottom: 3px;
      }
    }

    .signature-canvas-wrapper {
      padding: 5px;
      margin-bottom: 8px;

      .signature-canvas {
        border-radius: 6px;
      }
    }

    .signature-actions {
      gap: 8px;
      margin-bottom: 8px;

      .action-btn {
        height: 42px;
        font-size: 14px;
      }
    }

    .signature-preview {
      .preview-actions {
        flex-wrap: wrap;
        gap: 6px;

        .van-button {
          flex: 1;
          min-width: 100px;
        }
      }
    }
  }
}
</style>
