import axios from 'axios'

// 请求超时时间，300s
const requestTimeOut = 300 * 1000

// 提示信息显示时长
// const messageDuration = 5 * 1000

// 系统全局请求对象
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: requestTimeOut,
  responseType: 'json'
})

service.interceptors.request.use(
  config => {
    const _config = config
    const token = sessionStorage.getItem('TOKEN')
    _config.headers.token = token
    return _config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use((config) => {
  if (config.data.code === '0002') {
    localStorage.clear()
  }
  return config
}, (error) => {
  // if (error.response) {
  //   const errorMessage = error.response.data === null ? '系统内部异常，请联系网站管理员' : error.response.data.message
  // }
  return Promise.reject(error)
})

const request = {
  post (url, params) {
    return service.post(url, params, {
      transformRequest: [(params) => {
        return tansParams(params)
      }],
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },
  get (url, params) {
    let _params
    if (Object.is(params, undefined)) {
      _params = ''
    } else {
      _params = '?'
      for (const key in params) {
        if (params.hasOwnProperty(key) && params[key] !== null) {
          _params += `${key}=${params[key]}&`
        }
      }
    }
    return service.get(`${url}${_params}`)
  },
  postJson (url, params) {
    return service.post(url, params, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

const tansParams = (params) =>{
  let result = ''
  Object.keys(params).forEach((key) => {
    if (!Object.is(params[key], undefined) && !Object.is(params[key], null)) {
      result += encodeURIComponent(key) + '=' + encodeURIComponent(params[key]) + '&'
    }
  })
  return result
}

export default request
