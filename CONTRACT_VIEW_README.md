# 合同查看页面使用说明

## 功能概述

合同查看页面是一个基于Vue 2和Vant UI的移动端PDF合同查看和签名解决方案，支持以下功能：

- PDF文档的查看、翻页、缩放
- 移动端友好的用户界面
- 在PDF最后一页进行手写签名
- 签名叠加到PDF指定位置
- 横屏和竖屏自适应
- 签名数据保存和管理

## 技术栈

- **Vue 2.6.11** - 前端框架
- **Vant 2.10.3** - 移动端UI组件库
- **pdfh5 2.0.5** - PDF查看器库
- **Canvas API** - 手写签名实现

## 文件结构

```
src/
├── views/
│   ├── contractView/
│   │   └── index.vue          # 主要的合同查看组件
│   ├── contractTest/
│   │   └── index.vue          # 测试页面
│   └── signature/
│       └── index.vue          # 原有的签名组件（参考用）
├── router/
│   └── index.js               # 路由配置
└── components/
    └── footer/
        └── FooterMenu.vue     # 底部菜单（已添加合同查看入口）
```

## 使用方法

### 1. 访问合同查看功能

有两种方式访问合同查看功能：

#### 方式一：通过底部菜单
1. 在应用中点击底部的"我的"按钮
2. 在弹出的菜单中选择"合同查看"
3. 进入测试页面，可以选择查看默认合同或输入自定义PDF地址

#### 方式二：直接路由跳转
```javascript
// 查看默认PDF
this.$router.push({
  name: 'contractView'
})

// 查看指定PDF
this.$router.push({
  name: 'contractView',
  query: {
    pdfUrl: 'https://example.com/contract.pdf'
  }
})
```

### 2. PDF查看操作

- **翻页**：在PDF区域上下滑动
- **缩放**：使用底部的放大/缩小按钮，或双指缩放
- **重置缩放**：点击底部的重置按钮

### 3. 签名流程

1. 浏览PDF到最后一页
2. 在最后一页会显示"请在此处进行签名确认"提示
3. 点击"开始签名"按钮
4. 在弹出的签名画布上进行手写签名
5. 可以点击"清除"重新签名
6. 点击"确认签名"完成签名
7. 在预览弹窗中确认签名效果
8. 点击"应用到合同"将签名叠加到PDF上

## 配置选项

### PDF配置
在 `contractView/index.vue` 中可以修改PDF查看器的配置：

```javascript
this.pdfInstance = new Pdfh5('#pdfViewer', {
  pdfurl: this.pdfUrl,
  URIReplacement: true,    // 启用URI替换
  scrollEnable: true,      // 启用滚动
  zoomEnable: true,        // 启用缩放
  lazy: false,             // 禁用懒加载
  maxZoom: 3,              // 最大缩放比例
  minZoom: 0.5,            // 最小缩放比例
  on: {
    ready: this.onPdfReady,
    error: this.onPdfError,
    pageChange: this.onPageChange
  }
})
```

### 默认PDF文件
项目现在使用 `public` 目录下的本地PDF文件作为默认测试合同：
- 文件路径：`public/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf`
- 访问URL：`/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf`

如需更换默认PDF文件，只需：
1. 将新的PDF文件放入 `public` 目录
2. 修改 `getDefaultPdfUrl()` 方法中的文件路径

### 签名配置
可以在组件中修改签名相关的配置：

```javascript
// 画笔样式
this.ctx.strokeStyle = '#000000'  // 画笔颜色
this.ctx.lineWidth = 2            // 画笔粗细
this.ctx.lineCap = 'round'        // 画笔端点样式
this.ctx.lineJoin = 'round'       // 画笔连接样式

// 签名叠加位置
const signatureOverlay = document.createElement('div')
signatureOverlay.style.cssText = `
  position: absolute;
  bottom: 50px;    // 距离底部距离
  right: 50px;     // 距离右边距离
  width: 150px;    // 签名区域宽度
  height: 75px;    // 签名区域高度
  ...
`
```

## API接口集成

### 保存签名数据
在 `saveSignedContract` 方法中集成后端API：

```javascript
saveSignedContract() {
  const signatureData = {
    contractUrl: this.pdfUrl,
    signature: this.signatureImage,      // Base64格式的签名图片
    signaturePosition: {
      page: this.totalPages,
      x: 50,
      y: 50
    },
    timestamp: new Date().toISOString(),
    userInfo: {
      userId: localStorage.getItem('MERNO') || '',
      signTime: new Date().toLocaleString()
    }
  }

  // 调用后端API
  requestHTAPI.saveSignedContract(signatureData).then(res => {
    if (res.data.code === '0000') {
      this.$toast.success('合同签名保存成功')
    } else {
      this.$toast.fail('保存失败：' + res.data.message)
    }
  })
}
```

## 样式定制

### 主题色配置
项目使用统一的主题色配置，在 `src/styles/theme.less` 中定义：

```less
:root {
  --van-primary-color: #F37D05;
  --van-success-color: #F37D05;
  --van-danger-color: #F37D05;
}
```

### 响应式设计
组件支持横屏和竖屏自适应：

```less
// 横屏适配
@media screen and (orientation: landscape) {
  .signature-area {
    padding: 8px 16px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .header {
    padding: 10px 12px;
  }
}
```

## 注意事项

1. **PDF文件要求**：
   - 支持标准的PDF格式
   - 建议文件大小不超过10MB
   - 需要支持CORS跨域访问

2. **浏览器兼容性**：
   - 支持现代移动浏览器
   - 需要支持Canvas API
   - 建议在微信浏览器中测试

3. **性能优化**：
   - 大文件PDF可能加载较慢
   - 签名画布会根据屏幕尺寸自适应
   - 建议在WiFi环境下使用

4. **安全考虑**：
   - 签名数据以Base64格式存储
   - 建议在后端验证签名的完整性
   - 敏感合同建议加密传输

## 故障排除

### 常见问题

1. **PDF加载失败**
   - 检查PDF文件URL是否正确
   - 确认PDF文件支持跨域访问
   - 检查网络连接状态

2. **签名功能异常**
   - 确认浏览器支持Canvas API
   - 检查触摸事件是否被其他元素拦截
   - 尝试清除浏览器缓存

3. **样式显示问题**
   - 检查Vant组件是否正确引入
   - 确认CSS样式没有被覆盖
   - 检查移动端viewport设置

### 调试方法

1. 开启浏览器开发者工具
2. 查看Console中的错误信息
3. 检查Network面板中的资源加载情况
4. 使用移动端调试工具测试触摸事件

## 扩展功能

可以考虑添加的功能：

1. **多页签名**：支持在多个页面添加签名
2. **签名模板**：预设常用的签名样式
3. **批注功能**：支持在PDF上添加文字批注
4. **水印功能**：为签名后的PDF添加水印
5. **离线缓存**：支持PDF文件的离线查看
