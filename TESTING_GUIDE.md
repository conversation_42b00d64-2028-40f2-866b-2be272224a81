# 合同查看功能测试指南

## 快速测试步骤

### 1. 启动应用
确保开发服务器正在运行：
```bash
npm run serve-dev
```
应用将在 `http://localhost:8081/` 启动

### 2. 访问测试页面
有两种方式访问合同查看功能：

#### 方式一：通过应用菜单
1. 打开 `http://localhost:8081/`
2. 点击底部的"我的"按钮
3. 在弹出菜单中选择"合同查看"
4. 进入测试页面

#### 方式二：直接访问测试页面
直接访问：`http://localhost:8081/contract-test`

### 3. 测试默认电子合同
1. 在测试页面点击"查看默认电子合同"
2. 系统将加载 `public` 目录下的PDF文件：`0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf`
3. 测试以下功能：
   - **翻页**：上下滑动浏览PDF内容
   - **缩放**：点击底部的放大/缩小按钮
   - **重置缩放**：点击重置按钮恢复默认大小

### 4. 测试签名功能
1. 浏览PDF到最后一页
2. 在最后一页会显示"请在此处进行签名确认"提示
3. 点击"开始签名"按钮
4. 在弹出的签名画布上用手指或鼠标进行签名
5. 测试签名操作：
   - **绘制签名**：在画布上画出您的签名
   - **清除签名**：点击"清除"按钮重新签名
   - **确认签名**：点击"确认签名"查看预览
   - **应用签名**：在预览弹窗中点击"应用到合同"

### 5. 测试自定义PDF
1. 在测试页面点击"查看自定义PDF合同"
2. 在弹出的对话框中输入PDF文件的URL
3. 可以使用以下测试URL：
   - `https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf`
   - 或任何其他可访问的PDF文件URL
4. 点击确认加载自定义PDF

## 功能验证清单

### ✅ PDF查看功能
- [ ] PDF文件正常加载显示
- [ ] 可以正常翻页浏览
- [ ] 缩放功能正常工作
- [ ] 页面信息正确显示（当前页/总页数）
- [ ] 横屏竖屏切换正常
- [ ] 加载状态和错误提示正常

### ✅ 签名功能
- [ ] 最后一页显示签名提示
- [ ] 签名弹窗正常打开
- [ ] 手写签名绘制流畅
- [ ] 清除功能正常工作
- [ ] 签名预览正确显示
- [ ] 签名成功叠加到PDF上
- [ ] 签名位置正确（右下角）

### ✅ 用户体验
- [ ] 界面响应速度快
- [ ] 操作提示清晰明确
- [ ] 错误信息友好易懂
- [ ] 移动端触摸操作流畅
- [ ] 页面布局美观合理

## 常见问题排查

### 1. PDF加载失败
**现象**：显示"PDF加载失败"错误
**排查步骤**：
1. 检查PDF文件是否存在于 `public` 目录
2. 确认文件名是否正确：`0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf`
3. 检查浏览器控制台是否有网络错误
4. 尝试直接访问：`http://localhost:8081/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf`

### 2. 签名功能异常
**现象**：无法进行手写签名
**排查步骤**：
1. 确认浏览器支持Canvas API
2. 检查是否在最后一页（签名区域只在最后一页显示）
3. 尝试清除浏览器缓存重新加载
4. 检查触摸事件是否被其他元素拦截

### 3. 样式显示问题
**现象**：页面布局异常
**排查步骤**：
1. 确认Vant组件库正确加载
2. 检查CSS样式是否被覆盖
3. 验证移动端viewport设置
4. 尝试不同的浏览器和设备

## 性能测试

### 1. 加载性能
- 测试不同大小的PDF文件加载速度
- 验证大文件的内存使用情况
- 检查页面切换的响应时间

### 2. 签名性能
- 测试签名绘制的流畅度
- 验证签名图片的生成速度
- 检查签名叠加的处理时间

## 浏览器兼容性测试

### 推荐测试环境
- **移动端**：
  - iOS Safari (最新版本)
  - Android Chrome (最新版本)
  - 微信内置浏览器
- **桌面端**：
  - Chrome (最新版本)
  - Firefox (最新版本)
  - Safari (最新版本)

### 测试要点
- Canvas API支持
- 触摸事件处理
- PDF渲染效果
- 响应式布局

## 测试数据记录

建议记录以下测试数据：
- PDF文件大小和加载时间
- 不同设备的性能表现
- 签名功能的准确性
- 用户操作的便利性

## 反馈和改进

测试过程中如发现问题，请记录：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果
5. 浏览器和设备信息

这些信息将有助于进一步优化和改进功能。
