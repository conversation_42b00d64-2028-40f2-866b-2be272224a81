<template>
  <div class="tax-container">
    <!-- 年份选择器 -->
    <div class="filter">
      <van-sticky>
        <van-dropdown-menu active-color="#e92504">
          <van-dropdown-item
            title-class="dropdownTitle"
            @change="onYearChange"
            v-model="selectedYear"
            :options="yearOptions"
          />
        </van-dropdown-menu>
      </van-sticky>
    </div>

    <!-- 个税计算结果展示 -->
    <div v-if="taxGroups.length > 0" class="tax-results">
      <div class="results-header">
        <h3>个人所得税计算结果</h3>
        <div class="result-summary">
          <div class="summary-card">
            <div class="summary-icon income-icon">💰</div>
            <div class="summary-content">
              <div class="summary-label">累计收入</div>
              <div class="summary-value">{{ formatAmount(totalIncome) }}元</div>
            </div>
          </div>
          <div class="summary-card">
            <div class="summary-icon tax-icon">📊</div>
            <div class="summary-content">
              <div class="summary-label">累计应纳税额</div>
              <div class="summary-value highlight">{{ formatAmount(totalTax) }}元</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分组显示税费计算结果 -->
      <div class="tax-groups">
        <div
          v-for="(group, groupIndex) in taxGroups"
          :key="groupIndex"
          class="tax-group"
        >
          <div class="group-header">
            <div class="group-info">
              <span class="month-count">{{ group.data.length }}个月</span>
              <span class="group-title">{{ group.title }}</span>
            </div>
          </div>
          <div class="group-table">
            <table class="tax-table">
              <thead>
                <tr class="table-header">
                  <th class="col">月份</th>
                  <th class="col">当月收入</th>
                  <th class="col">累计收入</th>
                  <th class="col">累计成本扣除</th>
                  <th class="col">累计固定扣除</th>
                  <th class="col">累计应纳税所得额</th>
                  <th class="col">税率</th>
                  <th class="col">速算扣除数</th>
                  <th class="col">累计应缴税额</th>
                  <th class="col">上期累计税额</th>
                  <th class="col">本期应缴税额</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(result, index) in group.data" :key="index" class="table-row">
                  <td class="col month-col">
                    <span class="month-badge">{{ result.month }}</span>
                  </td>
                  <td class="col">{{ formatAmount(result.income) }}</td>
                  <td class="col">{{ formatAmount(result.cumulativeIncome) }}</td>
                  <td class="col">{{ formatAmount(result.cumulativeCost) }}</td>
                  <td class="col">{{ formatAmount(result.cumulativeFixedDeduct) }}</td>
                  <td class="col">{{ formatAmount(result.cumulativeTaxableIncome) }}</td>
                  <td class="col rate-col">{{ formatRate(result.rate) }}</td>
                  <td class="col">{{ formatAmount(result.quickDeduction) }}</td>
                  <td class="col">{{ formatAmount(result.cumulativeTax) }}</td>
                  <td class="col">{{ formatAmount(result.lastCumulativeTax) }}</td>
                  <td class="col tax-col">{{ formatAmount(result.currentTax) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-if="!loading && showNoData" class="no-data">
      <van-empty description="暂无税费数据" />
    </div>

  </div>
</template>

<script>
import requestHTAPI from '@/axios/HantangTax'
import { calculateTaxByMonthsGrouped } from 'china-tax-calculator'

export default {
  name: 'Tax',
  data () {
    return {
      loading: false,
      taxGroups: [], // 改为分组数据
      totalIncome: 0,
      totalTax: 0,
      selectedYear: new Date().getFullYear().toString(),
      yearOptions: [],
      showNoData: false
    }
  },
  mounted () {
    this.initYearOptions()
    this.queryPersonalTax()
  },
  methods: {
    // 初始化年份选项
    initYearOptions () {
      const currentYear = new Date().getFullYear()
      this.yearOptions = []
      for (let year = 2020; year <= currentYear; year++) {
        this.yearOptions.push({
          text: `${year}年`,
          value: year.toString()
        })
      }
    },

    // 年份变化事件
    onYearChange (value) {
      this.selectedYear = value
      this.queryPersonalTax()
    },

    // 查询个人所得税明细
    async queryPersonalTax () {
      this.loading = true
      this.showNoData = false
      this.taxGroups = []
      this.totalIncome = 0
      this.totalTax = 0

      try {
        const params = {
          year: this.selectedYear
        }
        const response = await requestHTAPI.queryTaxFee(params)
        console.log('wechat/queryTaxFee')
        console.log('个税明细查询结果:', response)

        if (response && response.data && response.data.code === '0000') {
          const apiData = response.data.data

          // 直接使用API数据，只提取必要的monthDay和taxBasis
          if (apiData && Array.isArray(apiData) && apiData.length > 0) {
            const validData = apiData.filter(item => item.taxBasis && parseFloat(item.taxBasis) > 0)
            if (validData.length > 0) {
              this.calculateTaxWithLibrary(validData)
              this.showNoData = false
            } else {
              this.showNoData = true
            }
          } else {
            this.showNoData = true
          }
        } else {
          this.$toast('查询失败：' + (response.data?.message || '未知错误'))
          this.showNoData = true
        }
      } catch (error) {
        console.error('查询个税明细失败:', error)
        this.$toast('查询失败，请稍后重试')
        this.showNoData = true
      } finally {
        this.loading = false
      }
    },

    // 使用china-tax-calculator计算税费
    calculateTaxWithLibrary (data) {
      try {
        // 直接使用原始API数据，提取monthDay和taxBasis
        const monthIncomes = data.map(item => item.taxBasis) // taxBasis已经是元单位
        const monthLabels = data.map(item => item.monthDay)

        // 使用china-tax-calculator的分组计算功能处理断档月份
        const taxGroups = calculateTaxByMonthsGrouped(monthIncomes, monthLabels)

        // 计算总收入和总税额
        let totalIncome = 0
        let totalTax = 0

        taxGroups.forEach(group => {
          group.data.forEach(result => {
            totalIncome += result.income
            totalTax += result.currentTax
          })
        })

        this.taxGroups = taxGroups
        this.totalIncome = totalIncome
        this.totalTax = totalTax
      } catch (error) {
        console.log(error)
        throw error
      }
    },

    // 格式化金额
    formatAmount (amount) {
      if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00'
      }
      return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 格式化税率
    formatRate (rate) {
      if (typeof rate !== 'number' || isNaN(rate)) {
        return '0.00%'
      }
      return (rate * 100).toFixed(2) + '%'
    }
  }
}
</script>

<style scoped>
.tax-groups {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.tax-group {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
}
.tax-container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.header h2 {
  color: #333;
  font-size: 40px;
  margin: 0;
  font-weight: 500;
}

.filter {
  margin-bottom: 0;
  background: white;
}

.filter /deep/ .van-dropdown-menu {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0;
}

.filter /deep/ .van-dropdown-menu__bar {
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.filter /deep/ .dropdownTitle {
  font-size: 32px;
  color: #333;
  font-weight: 400;
}

.content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}

.no-data {
  margin-top: 50px;
  padding: 30px 20px;
  background: white;
  border-radius: 8px;
  margin: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.tax-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tax-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.loading {
  color: #e92504;
  font-size: 28px;
  margin-top: 10px;
}

.tax-results {
  padding: 0 20px 20px 20px;
}

.results-header {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin: 12px;
  margin-bottom: 8px;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.results-header h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.result-summary {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.summary-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  max-width: 200px;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007bff;
  border-radius: 50%;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-content {
  flex: 1;
}

.summary-label {
  font-size: 28px;
  color: #666;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 36px;
  font-weight: 700;
  color: #333;
}

.summary-value.highlight {
  color: #007bff;
}

.group-header {
  display: flex;
  align-items: center;
  width: 100%;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.month-count {
  background: #007bff;
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 24px;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.group-title {
  font-size: 32px;
  font-weight: 500;
  color: #333;
}

.group-table {
  overflow-x: auto;
  overflow-y: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tax-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px;
}

.table-header {
  background: #007bff;
  color: white;
  font-weight: 500;
  font-size: 28px;
}

.table-row {
  transition: background-color 0.3s ease;
}

.table-row:nth-child(even) {
  background: #f8f9fa;
}

.table-row:hover {
  background: #f0f8ff;
}

.col {
  min-width: 100px;
  padding: 12px 8px;
  text-align: center;
  font-size: 24px;
  border-right: 1px solid #e0e0e0;
  white-space: nowrap;
  vertical-align: middle;
}

.col:first-child {
  min-width: 80px;
}

.col:last-child {
  border-right: none;
}

th.col {
  border-bottom: 1px solid #0056b3;
}

td.col {
  border-bottom: 1px solid #e0e0e0;
}

.month-col {
  text-align: center;
}

.month-badge {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 22px;
  font-weight: 500;
}

.rate-col {
  font-weight: 500;
  color: #007bff;
}

.tax-col {
  font-weight: 600;
  color: #007bff !important;
  background: rgba(0, 123, 255, 0.05);
  border-radius: 4px;
}

@media (max-width: 768px) {
  .tax-results {
    margin: 10px;
  }

  .results-header {
    padding: 20px 16px;
  }

  .results-header h3 {
    font-size: 40px;
  }

  .result-summary {
    flex-direction: column;
    gap: 12px;
  }

  .summary-card {
    max-width: none;
    padding: 16px;
  }

  .summary-icon {
    width: 40px;
    height: 40px;
    font-size: 40px;
  }

  .summary-value {
    font-size: 32px;
  }

  .group-header {
    padding: 12px 16px;
  }

  .group-header h4 {
    font-size: 28px;
  }

  .col {
    padding: 12px 6px;
    font-size: 24px;
  }

  .month-badge {
    padding: 4px 8px;
    font-size: 22px;
  }

  .table-header .col {
    padding: 12px 6px;
    font-size: 24px;
  }
}

.tax-item h3 {
  color: #e92504;
  margin: 0 0 10px 0;
  font-size: 36px;
}

.tax-item p {
  color: #666;
  margin: 0;
  line-height: 1.5;
}
</style>
