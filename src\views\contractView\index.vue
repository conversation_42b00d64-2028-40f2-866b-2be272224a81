<template>
  <div class="contract-view-container">
    <!-- 头部导航 -->
    <div class="header">
      <van-icon name="arrow-left" @click="goBack" />
      <span class="title">合同查看</span>
      <div class="header-right"></div>
    </div>

    <!-- PDF查看器容器 -->
    <div class="pdf-container" ref="pdfContainer">
      <div v-if="loading" class="loading-wrapper">
        <van-loading type="spinner" color="#1989fa">加载中...</van-loading>
      </div>

      <div v-if="error" class="error-wrapper">
        <van-empty description="PDF加载失败">
          <van-button round type="primary" @click="retryLoad">重新加载</van-button>
        </van-empty>
      </div>

      <!-- PDF显示区域 -->
      <div id="pdfViewer" class="pdf-viewer"></div>
    </div>

    <!-- 签名区域 - 只在最后一页显示 -->
    <div v-if="showSignatureArea" class="signature-area">
      <div class="signature-prompt">
        <van-icon name="edit" />
        <span>请在此处进行签名确认</span>
      </div>

      <van-button
        type="primary"
        size="large"
        round
        @click="startSignature"
        :disabled="signatureCompleted"
      >
        {{ signatureCompleted ? '签名已完成' : '开始签名' }}
      </van-button>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <div class="page-info">
        <span>{{ currentPage }} / {{ totalPages }}</span>
      </div>

      <div class="action-buttons">
        <van-button
          icon="minus"
          @click="zoomOut"
          :disabled="loading"
        />
        <van-button
          icon="plus"
          @click="zoomIn"
          :disabled="loading"
        />
        <van-button
          icon="replay"
          @click="resetZoom"
          :disabled="loading"
        />
      </div>
    </div>

    <!-- 签名弹窗 -->
    <van-popup
      v-model="showSignaturePopup"
      position="bottom"
      :style="{ height: '85%' }"
      closeable
      close-icon-position="top-right"
      @close="cancelSignature"
    >
      <div class="signature-popup-content">
        <div class="signature-popup-header">
          <h3>请在下方区域签名</h3>
          <p>签名将应用到合同最后一页</p>
        </div>

        <div class="signature-canvas-container">
          <canvas
            ref="signatureCanvas"
            class="signature-canvas"
            @touchstart="startDrawing"
            @touchmove="draw"
            @touchend="stopDrawing"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @mouseleave="stopDrawing"
          ></canvas>
        </div>

        <div class="signature-popup-actions">
          <van-button
            size="large"
            @click="clearSignature"
          >
            清除
          </van-button>
          <van-button
            size="large"
            @click="cancelSignature"
          >
            取消
          </van-button>
          <van-button
            type="primary"
            size="large"
            @click="confirmSignature"
            :disabled="!hasSignature"
          >
            确认签名
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 签名预览弹窗 -->
    <van-popup
      v-model="showSignaturePreview"
      position="center"
      closeable
    >
      <div class="signature-preview-content">
        <h3>签名预览</h3>
        <div class="preview-image-wrapper">
          <img v-if="signatureImage" :src="signatureImage" alt="签名预览" />
        </div>
        <div class="preview-actions">
          <van-button @click="showSignaturePreview = false">关闭</van-button>
          <van-button type="primary" @click="applySignatureToPdf">应用到合同</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Pdfh5 from 'pdfh5'

export default {
  name: 'ContractView',
  data () {
    return {
      // PDF相关
      pdfInstance: null,
      loading: true,
      error: false,
      currentPage: 1,
      totalPages: 0,
      pdfUrl: '',

      // 签名相关
      showSignatureArea: false,
      showSignaturePopup: false,
      showSignaturePreview: false,
      signatureImage: null,
      signatureCompleted: false,

      // 签名画布相关
      canvas: null,
      ctx: null,
      isDrawing: false,
      hasSignature: false,
      lastX: 0,
      lastY: 0,

      // 缩放相关
      currentScale: 1,
      minScale: 0.5,
      maxScale: 3
    }
  },
  mounted () {
    this.initPdf()
    this.handleOrientationChange()
    window.addEventListener('orientationchange', this.handleOrientationChange)
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    if (this.pdfInstance) {
      this.pdfInstance.destroy()
    }
    window.removeEventListener('orientationchange', this.handleOrientationChange)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化PDF
    initPdf () {
      // 从路由参数获取PDF URL，如果没有则使用默认的测试PDF
      this.pdfUrl = this.$route.query.pdfUrl || this.getDefaultPdfUrl()

      this.loading = true
      this.error = false

      try {
        this.pdfInstance = new Pdfh5('#pdfViewer', {
          pdfurl: this.pdfUrl,
          URIReplacement: true,
          scrollEnable: true,
          zoomEnable: true,
          lazy: false,
          maxZoom: 3,
          minZoom: 0.5,
          on: {
            ready: this.onPdfReady,
            error: this.onPdfError,
            pageChange: this.onPageChange
          }
        })
      } catch (err) {
        console.error('PDF初始化失败:', err)
        this.onPdfError(err)
      }
    },

    // 获取默认PDF URL
    getDefaultPdfUrl () {
      // 使用项目public目录下的示例PDF文件
      return '/0314e6b7244847fd907fc2dc694aa9d1_电子合同.pdf'
    },

    // PDF加载完成
    onPdfReady (pdf) {
      console.log('PDF加载完成:', pdf)
      this.loading = false
      this.totalPages = pdf.numPages
      this.currentPage = 1
      this.checkIfLastPage()
    },

    // PDF加载错误
    onPdfError (error) {
      console.error('PDF加载错误:', error)
      this.loading = false
      this.error = true
      this.$toast.fail('PDF加载失败，请检查网络连接')
    },

    // 页面变化
    onPageChange (page) {
      this.currentPage = page
      this.checkIfLastPage()
    },

    // 检查是否为最后一页
    checkIfLastPage () {
      this.showSignatureArea = this.currentPage === this.totalPages
    },

    // 重新加载PDF
    retryLoad () {
      this.initPdf()
    },

    // 缩放操作
    zoomIn () {
      if (this.currentScale < this.maxScale) {
        this.currentScale += 0.2
        this.pdfInstance && this.pdfInstance.zoomTo(this.currentScale)
      }
    },

    zoomOut () {
      if (this.currentScale > this.minScale) {
        this.currentScale -= 0.2
        this.pdfInstance && this.pdfInstance.zoomTo(this.currentScale)
      }
    },

    resetZoom () {
      this.currentScale = 1
      this.pdfInstance && this.pdfInstance.zoomTo(this.currentScale)
    },

    // 开始签名
    startSignature () {
      this.showSignaturePopup = true
      this.$nextTick(() => {
        this.initSignatureCanvas()
      })
    },

    // 初始化签名画布
    initSignatureCanvas () {
      this.canvas = this.$refs.signatureCanvas
      if (!this.canvas) return

      this.ctx = this.canvas.getContext('2d')

      // 设置画布尺寸
      const container = this.canvas.parentElement
      const containerWidth = container.clientWidth - 32 // 减去padding
      const containerHeight = container.clientHeight - 32

      this.canvas.width = containerWidth
      this.canvas.height = containerHeight

      // 设置画布的CSS尺寸与实际尺寸一致
      this.canvas.style.width = this.canvas.width + 'px'
      this.canvas.style.height = this.canvas.height + 'px'

      // 设置画笔样式
      this.ctx.strokeStyle = '#000000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
      this.ctx.lineJoin = 'round'
      this.ctx.globalCompositeOperation = 'source-over'
    },

    // 获取事件位置
    getEventPos (e) {
      if (!this.canvas) return { x: 0, y: 0 }

      const rect = this.canvas.getBoundingClientRect()
      const clientX = e.clientX || (e.touches && e.touches[0].clientX)
      const clientY = e.clientY || (e.touches && e.touches[0].clientY)

      const scaleX = this.canvas.width / rect.width
      const scaleY = this.canvas.height / rect.height

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY
      }
    },

    // 开始绘制
    startDrawing (e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = true
      const pos = this.getEventPos(e)
      this.lastX = pos.x
      this.lastY = pos.y

      this.ctx.beginPath()
      this.ctx.moveTo(this.lastX, this.lastY)
      this.ctx.lineTo(pos.x, pos.y)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo(pos.x, pos.y)
    },

    // 绘制
    draw (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()

      const pos = this.getEventPos(e)

      this.ctx.quadraticCurveTo(this.lastX, this.lastY, (pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)
      this.ctx.stroke()
      this.ctx.beginPath()
      this.ctx.moveTo((pos.x + this.lastX) / 2, (pos.y + this.lastY) / 2)

      this.lastX = pos.x
      this.lastY = pos.y
      this.hasSignature = true
    },

    // 停止绘制
    stopDrawing (e) {
      if (!this.isDrawing) return
      e.preventDefault()
      e.stopPropagation()
      this.isDrawing = false
      this.ctx.closePath()
    },

    // 清除签名
    clearSignature () {
      if (this.ctx && this.canvas) {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
        this.hasSignature = false
      }
    },

    // 取消签名
    cancelSignature () {
      this.showSignaturePopup = false
      this.clearSignature()
    },

    // 确认签名
    confirmSignature () {
      if (!this.hasSignature) {
        this.$toast('请先进行签名')
        return
      }

      this.signatureImage = this.canvas.toDataURL('image/png')
      this.showSignaturePopup = false
      this.showSignaturePreview = true
    },

    // 签名保存回调
    onSignatureSaved (signatureData) {
      this.signatureImage = signatureData
    },

    // 应用签名到PDF
    applySignatureToPdf () {
      if (!this.signatureImage) {
        this.$toast('没有可用的签名')
        return
      }

      // 创建签名叠加层
      this.createSignatureOverlay()

      this.signatureCompleted = true
      this.showSignaturePreview = false

      this.$toast.success('签名已应用到合同')

      // 调用API保存签名后的合同
      this.saveSignedContract()
    },

    // 创建签名叠加层
    createSignatureOverlay () {
      // 在PDF最后一页添加签名叠加层
      const pdfViewer = document.getElementById('pdfViewer')
      if (!pdfViewer) return

      // 查找最后一页的容器
      const pages = pdfViewer.querySelectorAll('.pdfPage')
      if (pages.length === 0) return

      const lastPage = pages[pages.length - 1]

      // 移除已存在的签名叠加层
      const existingOverlay = lastPage.querySelector('.signature-overlay')
      if (existingOverlay) {
        existingOverlay.remove()
      }

      // 创建签名叠加层
      const signatureOverlay = document.createElement('div')
      signatureOverlay.className = 'signature-overlay'
      signatureOverlay.style.cssText = `
        position: absolute;
        bottom: 50px;
        right: 50px;
        width: 150px;
        height: 75px;
        z-index: 10;
        pointer-events: none;
        border: 1px solid #ddd;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        padding: 5px;
        box-sizing: border-box;
      `

      // 创建签名图片
      const signatureImg = document.createElement('img')
      signatureImg.src = this.signatureImage
      signatureImg.style.cssText = `
        width: 100%;
        height: 100%;
        object-fit: contain;
      `

      signatureOverlay.appendChild(signatureImg)
      lastPage.style.position = 'relative'
      lastPage.appendChild(signatureOverlay)
    },

    // 保存签名后的合同
    saveSignedContract () {
      // 准备签名数据
      const signatureData = {
        contractUrl: this.pdfUrl,
        signature: this.signatureImage,
        signaturePosition: {
          page: this.totalPages,
          x: 50, // 签名位置X坐标（相对于页面右边距）
          y: 50 // 签名位置Y坐标（相对于页面底边距）
        },
        timestamp: new Date().toISOString(),
        userInfo: {
          // 可以添加用户信息
          userId: localStorage.getItem('MERNO') || '',
          signTime: new Date().toLocaleString()
        }
      }

      console.log('保存签名后的合同', signatureData)

      // 这里可以调用后端API保存签名信息
      // 例如：
      // requestHTAPI.saveSignedContract(signatureData).then(res => {
      //   if (res.data.code === '0000') {
      //     this.$toast.success('合同签名保存成功')
      //   } else {
      //     this.$toast.fail('保存失败：' + res.data.message)
      //   }
      // })

      // 模拟保存成功
      setTimeout(() => {
        this.$toast.success('合同签名已保存')
      }, 500)
    },

    // 处理屏幕方向变化
    handleOrientationChange () {
      setTimeout(() => {
        this.handleResize()
      }, 300)
    },

    // 处理窗口大小变化
    handleResize () {
      if (this.pdfInstance) {
        this.pdfInstance.resize()
      }
    },

    // 返回上一页
    goBack () {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="less">
.contract-view-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;

  .van-icon {
    font-size: 20px;
    color: #333;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .header-right {
    width: 20px;
  }
}

.pdf-container {
  flex: 1;
  position: relative;
  overflow: hidden;

  .loading-wrapper,
  .error-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .pdf-viewer {
    width: 100%;
    height: 100%;
  }
}

.signature-area {
  background: white;
  padding: 16px;
  border-top: 1px solid #eee;
  text-align: center;

  .signature-prompt {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    color: #666;
    font-size: 14px;

    .van-icon {
      margin-right: 6px;
      color: #1989fa;
    }
  }
}

.bottom-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #eee;

  .page-info {
    font-size: 14px;
    color: #666;
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .van-button {
      width: 36px;
      height: 36px;
      padding: 0;
    }
  }
}

.signature-popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .signature-popup-header {
    padding: 16px;
    text-align: center;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .signature-canvas-container {
    flex: 1;
    padding: 16px;
    background: #f8f9fa;

    .signature-canvas {
      width: 100%;
      height: 100%;
      background: white;
      border: 2px dashed #ddd;
      border-radius: 8px;
      cursor: crosshair;
      touch-action: none;

      &:hover {
        border-color: #1989fa;
      }
    }
  }

  .signature-popup-actions {
    display: flex;
    gap: 12px;
    padding: 16px;
    border-top: 1px solid #eee;

    .van-button {
      flex: 1;
    }
  }
}

.signature-preview-content {
  padding: 20px;
  text-align: center;

  h3 {
    margin-bottom: 16px;
    color: #333;
  }

  .preview-image-wrapper {
    margin-bottom: 20px;

    img {
      max-width: 200px;
      max-height: 100px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  }

  .preview-actions {
    display: flex;
    gap: 12px;

    .van-button {
      flex: 1;
    }
  }
}

// 横屏适配
@media screen and (orientation: landscape) {
  .signature-area {
    padding: 8px 16px;

    .signature-prompt {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }

  .bottom-actions {
    padding: 8px 16px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .header {
    padding: 10px 12px;

    .title {
      font-size: 16px;
    }
  }

  .signature-area {
    padding: 12px;
  }

  .bottom-actions {
    padding: 10px 12px;

    .action-buttons .van-button {
      width: 32px;
      height: 32px;
    }
  }
}
</style>
