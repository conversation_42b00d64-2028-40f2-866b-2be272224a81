<template>
  <div class="contract-test-container">
    <div class="header">
      <h2>合同查看测试页面</h2>
      <p>点击下方按钮测试合同查看功能</p>
    </div>

    <div class="test-options">
      <van-cell-group>
        <van-cell
          title="查看默认电子合同"
          label="使用项目中的示例PDF文件"
          is-link
          @click="viewDefaultContract"
        />

        <van-cell
          title="查看自定义PDF合同"
          is-link
          @click="showCustomUrlDialog = true"
        />
      </van-cell-group>
    </div>

    <div class="instructions">
      <van-notice-bar
        left-icon="info-o"
        text="合同查看页面支持PDF浏览、缩放、翻页，并在最后一页提供手写签名功能。默认使用项目中的示例电子合同文件。"
      />
    </div>

    <!-- 自定义URL输入对话框 -->
    <van-dialog
      v-model="showCustomUrlDialog"
      title="输入PDF地址"
      show-cancel-button
      @confirm="viewCustomContract"
    >
      <van-field
        v-model="customPdfUrl"
        placeholder="请输入PDF文件的URL地址"
        type="url"
      />
    </van-dialog>
  </div>
</template>

<script>
export default {
  name: 'ContractTest',
  data () {
    return {
      showCustomUrlDialog: false,
      customPdfUrl: ''
    }
  },
  methods: {
    // 查看默认测试合同
    viewDefaultContract () {
      this.$router.push({
        name: 'contractView'
      })
    },

    // 查看自定义PDF合同
    viewCustomContract () {
      if (!this.customPdfUrl.trim()) {
        this.$toast('请输入有效的PDF地址')
        return
      }

      this.$router.push({
        name: 'contractView',
        query: {
          pdfUrl: this.customPdfUrl.trim()
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.contract-test-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 24px;

  h2 {
    color: #333;
    font-size: 20px;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.test-options {
  margin-bottom: 24px;

  .van-cell-group {
    border-radius: 8px;
    overflow: hidden;
  }
}

.instructions {
  .van-notice-bar {
    border-radius: 8px;
  }
}

/deep/ .van-dialog {
  .van-field {
    margin: 16px 0;
  }
}
</style>
