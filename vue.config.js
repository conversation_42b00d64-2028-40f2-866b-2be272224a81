const name = '链接云' // page title

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: true,
  devServer: {
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      // 系统接口
      [process.env.VUE_APP_BASE_API]: {
        target: '',
        changeOrigin: true,
        logLevel: 'debug',
        ws: false,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },

      // 解决预览pdf跨域
      '/pdf': {
        target: process.env.VUE_APP_PDF_API,
        changeOrigin: true,
        logLevel: 'debug',
        ws: false,
        pathRewrite: {
          '^/pdf': ''
        }
      },

      // 山西活体认证授权
      '/auth': {
        target: process.env.VUE_APP_BASELETSP_API,
        changeOrigin: true,
        logLevel: 'debug',
        ws: false,
        pathRewrite: {
          '^/auth': '/auth'
        }
      },

      // 山西活体认证
      '/api': {
        target: process.env.VUE_APP_BASELETSP_API,
        changeOrigin: true,
        logLevel: 'debug',
        ws: false,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },
  configureWebpack: {
    name: name
  }
}
