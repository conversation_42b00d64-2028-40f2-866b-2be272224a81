<template>
  <div class="endTask">
    <div class="endTask-layout">
      <van-form @submit="onSubmit" >
        <van-field
          v-model="message"
          name="message"
          rows="5"
          autosize
          label="备注"
          type="textarea"
          maxlength="150"
          placeholder="请输入备注"
          show-word-limit
          :rules="[{ required: true }]"
        />
        <van-field name="uploader" label="上传图片">
          <template #input>
            <van-uploader v-model="fileList" multiple />
          </template>
        </van-field>

        <div style="margin: 16px;">
          <van-button size="large" color="linear-gradient(to right, #ff6034, #ee0a24)" native-type="submit">
            提交
          </van-button>
        </div>
      </van-form>
    </div>

  </div>
</template>
<script>
export default {
  name: 'EndTask',
  data () {
    return {
      message: '',
      fileList: []
    }
  },
  methods: {
    onSubmit (values) {
      console.log('submit', values)
    }
  }
}
</script>
<style scoped>

  .endTask{
    overflow: hidden;
    height: 100%;
  }
  .endTask-layout{
    padding-top: 40px;
  }
</style>
